@extends('theme.layout.master')

@push('css')
@endpush

@section('content')
    <section class="create-dashboard">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white_box table_box">
                        <div class="user_engagement">
                            <h5>Create Account Manager</h5>
                        </div>
                        <form class="fitter_forms" id="staffForm" action="{{ route('account-managers.store') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="image_box">
                                        <label for="uploadFile">Profile Picture <span class="required-star">*</span></label>
                                        <div class="upload-box">
                                            <input type="file" class="uploadFile" name="image" accept="image/*" required>
                                            <div class="upload-content" id="">
                                                <img class="img-fluid" src="{{ asset('website') }}/assets/images/upload.png" class="uploadIcon">
                                                <p>Upload Image</p>
                                            </div>
                                            <img class="hidden imagePreview">
                                        </div>
                                        @error('image')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="firstName">First Name <span class="required-star">*</span></label>
                                        <input type="text" class="form-control" id="firstName" name="first_name" placeholder="Enter first name" value="{{ old('first_name') }}">
                                        @error('first_name')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="lastName">Last Name <span class="required-star">*</span></label>
                                        <input type="text" class="form-control" id="lastName" name="last_name" placeholder="Enter last name" value="{{ old('last_name') }}">
                                        @error('last_name')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="phone">Phone Number <span class="required-star">*</span></label>
                                        <input type="tel" class="form-control" id="phone" name="phone" placeholder="Enter phone number" value="{{ old('phone') }}">
                                        @error('phone')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email">Email <span class="required-star">*</span></label>
                                        <input type="email" class="form-control" id="email" name="email" placeholder="Enter email" value="{{ old('email') }}">
                                        @error('email')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="bio">Bio <span class="required-star">*</span></label>
                                        <textarea class="form-control" id="bio" name="bio" rows="4" placeholder="Enter bio">{{ old('bio') }}</textarea>
                                        @error('bio')
                                            <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="create_btns">
                                        <button type="button" onclick="submitForm()" class="btn light_green_btn">
                                            Create
                                        </button>
                                        <button type="button" class="btn cancel_btn go_back">Cancel</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@push('js')

    <script>

        $(document).ready(function () {
            function previewImage(event) {
                let file = event.target.files[0];
                let acceptTypes = $(event.target).attr('accept')?.split(',').map(type => type.trim()) || [];
                if (acceptTypes.length === 0) {
                    acceptTypes = ['image/*', '.svg'];
                }
                let isValid = false;

                if (acceptTypes.includes('image/*') && file.type.startsWith('image/')) {
                    isValid = true;
                }

                if (acceptTypes.includes('.svg') && file.name.endsWith('.svg')) {
                    isValid = true;
                }

                if (!isValid) {
                    alert("Please upload a valid file.");
                    $(event.target).val("");
                    return;
                }
                let reader = new FileReader();
                reader.onload = e => {
                    let container = $(event.target).closest('.image_box');
                    container.find('.imagePreview').attr("src", e.target.result).show();
                    container.find('.upload-content').hide();
                };
                reader.readAsDataURL(file);
            }

            $(".uploadFile").on("change", function (event) {
                previewImage(event);
            });
        });

        $('#staffForm').validate({
            rules: {
                first_name: {
                    required: true,
                    minlength: 2
                },
                last_name: {
                    required: true,
                    minlength: 2
                },
                email: {
                    required: true,
                    email: true,
                },
                phone: {
                    required: true,
                },
                bio: {
                    required: true,
                    minlength: 10,
                },
                image: {
                    required: true,
                    // extension: "jpeg|jpg|png|gif"
                }
            },
            messages: {
                first_name: {
                    required: "Please enter first name",
                    minlength: "First name must be at least 2 characters long"
                },
                last_name: {
                    required: "Please enter last name",
                    minlength: "Last name must be at least 2 characters long"
                },
                email: {
                    required: "Please enter an email address",
                    email: "Please enter a valid email address",
                },
                phone: {
                    required: "Please enter your phone number",
                },
                bio: {
                    required: "Please enter a bio",
                    minlength: "Bio must be at least 10 characters long"
                },
                image: {
                    required: "Please upload an image",
                    // extension: "Please upload a valid image file (PNG, JPEG, JPG, GIF)"
                }
            },
            onfocusout: function (element) {
                this.element(element);
            },
            onkeyup: function (element) {
                this.element(element);
            },
            onchange: function (element) {
                this.element(element);
            },
            submitHandler: function (form) {
                form.submit()
            }
        });

    </script>
@endpush
